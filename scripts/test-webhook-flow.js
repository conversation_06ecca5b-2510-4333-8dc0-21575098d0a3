#!/usr/bin/env node

/**
 * Manual test script for the test webhook flow
 * 
 * This script:
 * 1. Creates a test user
 * 2. Shows their test email address
 * 3. Simulates sending an email to that address
 * 4. Verifies the email was processed correctly
 * 5. Shows the webhook payload
 * 
 * Usage: node scripts/test-webhook-flow.js
 */

import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcrypt';
import { randomBytes } from 'crypto';

const prisma = new PrismaClient();

// ANSI color codes for pretty output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logStep(step, message) {
  log(`\n${step}. ${message}`, 'cyan');
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

async function createTestUser() {
  const email = `test-${randomBytes(4).toString('hex')}@example.com`;
  const password = 'testpassword123';
  const hashedPassword = await bcrypt.hash(password, 10);

  const user = await prisma.user.create({
    data: {
      email,
      password: hashedPassword,
      name: 'Test User',
      verified: true
    }
  });

  return { user, password };
}

async function simulateEmailProcessing(testEmail, messageId) {
  const rawEmail = `From: <EMAIL>
To: ${testEmail}
Subject: Manual Test Email
Date: ${new Date().toUTCString()}
Message-ID: ${messageId}
MIME-Version: 1.0
Content-Type: text/plain; charset=UTF-8

Hello from the manual test script!

This email is being sent to test the webhook functionality.
If you can see this in your logs with a webhook payload, everything is working correctly!

Features being tested:
- User ID suffix extraction
- Test webhook processing
- Payload storage
- Usage tracking

Best regards,
Test Script`;

  // Simulate the email processing logic
  const userIdSuffix = testEmail.split('@')[0];
  
  // Find user by suffix
  const user = await prisma.user.findFirst({
    where: {
      id: { endsWith: userIdSuffix }
    }
  });

  if (!user) {
    throw new Error(`No user found with ID ending in: ${userIdSuffix}`);
  }

  // Check usage limit
  const now = new Date();
  const lastReset = new Date(user.lastUsageReset);
  
  let currentUsage = user.currentMonthEmails;
  if (now.getMonth() !== lastReset.getMonth() || now.getFullYear() !== lastReset.getFullYear()) {
    currentUsage = 0;
  }

  if (currentUsage >= user.monthlyEmailLimit) {
    throw new Error(`User has exceeded monthly limit: ${currentUsage}/${user.monthlyEmailLimit}`);
  }

  // Create webhook payload (simplified version)
  const webhookPayload = {
    message: {
      sender: {
        name: null,
        email: '<EMAIL>'
      },
      recipient: {
        name: null,
        email: testEmail
      },
      subject: 'Manual Test Email',
      content: {
        text: rawEmail.split('\n\n').slice(1).join('\n\n'),
        html: null
      },
      date: new Date().toISOString(),
      attachments: []
    },
    envelope: {
      messageId,
      processed: {
        timestamp: new Date().toISOString(),
        domain: 'web.xadi.eu'
      }
    }
  };

  // Store email record
  const emailRecord = await prisma.email.create({
    data: {
      messageId,
      fromAddress: '<EMAIL>',
      toAddresses: [testEmail],
      subject: 'Manual Test Email',
      domainId: null,
      webhookPayload,
      isTestWebhook: true,
      deliveryStatus: 'DELIVERED',
      deliveredAt: new Date(),
      expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
    }
  });

  // Note: Test webhooks should NOT increment usage count
  // This is intentionally commented out to avoid counting test emails toward limits

  return { emailRecord, user };
}

async function main() {
  try {
    log('🚀 Testing Test Webhook Flow', 'bright');
    log('================================', 'bright');

    // Step 1: Create test user
    logStep(1, 'Creating test user...');
    const { user, password } = await createTestUser();
    logSuccess(`Created user: ${user.email}`);
    logInfo(`User ID: ${user.id}`);

    // Step 2: Generate test email
    logStep(2, 'Generating test email address...');
    const userIdSuffix = user.id.slice(-8);
    const testEmail = `${userIdSuffix}@web.xadi.eu`;
    logSuccess(`Test email: ${testEmail}`);
    logInfo(`User ID suffix: ${userIdSuffix}`);

    // Step 3: Simulate email processing
    logStep(3, 'Simulating email processing...');
    const messageId = `<manual-test-${Date.now()}@example.com>`;
    const { emailRecord } = await simulateEmailProcessing(testEmail, messageId);
    logSuccess('Email processed successfully!');
    logInfo(`Message ID: ${messageId}`);
    logInfo(`Email Record ID: ${emailRecord.id}`);

    // Step 4: Verify storage
    logStep(4, 'Verifying email storage...');
    const storedEmail = await prisma.email.findUnique({
      where: { messageId }
    });

    if (!storedEmail) {
      throw new Error('Email not found in database');
    }

    logSuccess('Email found in database');
    logInfo(`Delivery Status: ${storedEmail.deliveryStatus}`);
    logInfo(`Is Test Webhook: ${storedEmail.isTestWebhook}`);
    logInfo(`Has Payload: ${!!storedEmail.webhookPayload}`);

    // Step 5: Show webhook payload
    logStep(5, 'Webhook payload preview...');
    const payload = storedEmail.webhookPayload;
    
    log('\n📋 Webhook Payload Structure:', 'yellow');
    log('─'.repeat(50), 'yellow');
    log(JSON.stringify({
      message: {
        sender: payload.message.sender,
        recipient: payload.message.recipient,
        subject: payload.message.subject,
        content: {
          text: payload.message.content.text.substring(0, 100) + '...',
          html: payload.message.content.html
        }
      },
      envelope: {
        messageId: payload.envelope.messageId,
        processed: payload.envelope.processed
      }
    }, null, 2), 'yellow');

    // Step 6: Verify usage tracking
    logStep(6, 'Checking usage tracking...');
    const updatedUser = await prisma.user.findUnique({
      where: { id: user.id },
      select: { currentMonthEmails: true, monthlyEmailLimit: true }
    });

    logSuccess(`Usage updated: ${updatedUser.currentMonthEmails}/${updatedUser.monthlyEmailLimit}`);

    // Step 7: Summary
    log('\n🎉 Test Completed Successfully!', 'green');
    log('═'.repeat(50), 'green');
    log(`✅ User created: ${user.email}`, 'green');
    log(`✅ Test email: ${testEmail}`, 'green');
    log(`✅ Email processed and stored`, 'green');
    log(`✅ Webhook payload generated`, 'green');
    log(`✅ Usage tracking working`, 'green');

    log('\n📝 Next Steps:', 'cyan');
    log('1. Set up MX record: web.xadi.eu MX 10 mw.xadi.eu', 'cyan');
    log('2. Send a real email to the test address', 'cyan');
    log('3. Check the logs in your application', 'cyan');
    log('4. Verify WebSocket real-time updates', 'cyan');

    // Cleanup
    logStep(7, 'Cleaning up test data...');
    await prisma.email.delete({ where: { id: emailRecord.id } });
    await prisma.user.delete({ where: { id: user.id } });
    logSuccess('Test data cleaned up');

  } catch (error) {
    logError(`Test failed: ${error.message}`);
    console.error(error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Handle graceful shutdown
process.on('SIGINT', async () => {
  log('\n\n👋 Test interrupted by user', 'yellow');
  await prisma.$disconnect();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  log('\n\n👋 Test terminated', 'yellow');
  await prisma.$disconnect();
  process.exit(0);
});

main().catch(async (error) => {
  logError(`Unhandled error: ${error.message}`);
  console.error(error);
  await prisma.$disconnect();
  process.exit(1);
});
