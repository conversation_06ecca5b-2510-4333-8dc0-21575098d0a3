#!/usr/bin/env node

/**
 * Post-build script to fix og:image URL with correct hashed filename
 * This ensures the og:image meta tag always points to the correct asset
 */

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

const DIST_DIR = path.join(__dirname, '../dist/frontend')
const INDEX_HTML = path.join(DIST_DIR, 'index.html')
const ASSETS_DIR = path.join(DIST_DIR, 'assets')

try {
  // Find asset files in assets directory
  const assetFiles = fs.readdirSync(ASSETS_DIR)
  const ogImageFile = assetFiles.find(file => file.startsWith('og-image-') && file.endsWith('.png'))
  const androidChrome192File = assetFiles.find(file => file.startsWith('android-chrome-192x192-') && file.endsWith('.png'))
  const androidChrome512File = assetFiles.find(file => file.startsWith('android-chrome-512x512-') && file.endsWith('.png'))
  const webmanifestFile = assetFiles.find(file => file.startsWith('site-') && file.endsWith('.webmanifest'))

  if (!ogImageFile) {
    console.log('⚠️  No og-image asset found, skipping...')
    process.exit(0)
  }

  // Fix index.html og:image URL
  let htmlContent = fs.readFileSync(INDEX_HTML, 'utf8')
  const ogImageRegex = /(content="https:\/\/mw\.xadi\.eu\/)src\/frontend\/assets\/images\/og-image\.png"/g
  const newHtmlContent = htmlContent.replace(ogImageRegex, `$1assets/${ogImageFile}"`)

  if (newHtmlContent !== htmlContent) {
    fs.writeFileSync(INDEX_HTML, newHtmlContent)
    console.log(`✅ Updated og:image URL to use ${ogImageFile}`)
  } else {
    console.log('ℹ️  og:image URL already correct')
  }

  // Fix webmanifest icon URLs if files exist
  if (webmanifestFile && androidChrome192File && androidChrome512File) {
    const webmanifestPath = path.join(ASSETS_DIR, webmanifestFile)
    let webmanifestContent = fs.readFileSync(webmanifestPath, 'utf8')

    // Replace generic asset paths with hashed filenames
    webmanifestContent = webmanifestContent
      .replace(/\/assets\/android-chrome-192x192\.png/g, `/assets/${androidChrome192File}`)
      .replace(/\/assets\/android-chrome-512x512\.png/g, `/assets/${androidChrome512File}`)

    fs.writeFileSync(webmanifestPath, webmanifestContent)
    console.log(`✅ Updated webmanifest icon URLs`)
  }
  
} catch (error) {
  console.error('❌ Error fixing og:image URL:', error.message)
  process.exit(1)
}
