<!DOCTYPE html>
<html lang="en" data-theme="emerald">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/x-icon" href="/src/frontend/assets/images/favicon.ico" />
    <link rel="manifest" href="/src/frontend/assets/images/site.webmanifest" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="The easiest way to turn incoming emails into webhooks — 100% hosted in the EU, fully GDPR-compliant. Works with Zapier, Make, n8n, and your own stack." />
    <meta name="keywords" content="email, webhook, GDPR, EU, compliance, developer, API" />
    <title>Mail2Webhook.eu – Turn emails into webhooks, GDPR-style</title>
    <meta property="og:title" content="Mail2Webhook.eu – Turn emails into webhooks, GDPR-style" />
    <meta property="og:description" content="The easiest way to turn incoming emails into webhooks — 100% hosted in the EU, fully GDPR-compliant. Works with Zapier, Make, n8n, and your own stack." />
    <meta property="og:image" content="https://mw.xadi.eu/assets/og-image.png" />
    <meta property="og:url" content="https://mw.xadi.eu" />
    <meta name="twitter:card" content="summary_large_image" />
    <link rel="apple-touch-icon" sizes="180x180" href="/src/frontend/assets/images/apple-touch-icon.png" />
    <link rel="icon" type="image/png" sizes="32x32" href="/src/frontend/assets/images/favicon-32x32.png" />
    <link rel="icon" type="image/png" sizes="16x16" href="/src/frontend/assets/images/favicon-16x16.png" />
  </head>
  <body>
    <div id="app"></div>
    <script type="module" src="/src/frontend/main.ts"></script>
  </body>
</html>
