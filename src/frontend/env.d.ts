/// <reference types="vite/client" />

declare module '*.vue' {
  import type { DefineComponent } from 'vue'
  const component: DefineComponent<Record<string, unknown>, Record<string, unknown>, unknown>
  export default component
}

declare module '*.png' {
  const src: string
  export default src
}

declare module '*.jpg' {
  const src: string
  export default src
}

declare module '*.jpeg' {
  const src: string
  export default src
}

declare module '*.gif' {
  const src: string
  export default src
}

declare module '*.webp' {
  const src: string
  export default src
}

declare module '*.svg' {
  const src: string
  export default src
}

declare module '*.ico' {
  const src: string
  export default src
}

declare module '*.webmanifest' {
  const src: string
  export default src
}

// Global types
declare global {
  /* eslint-disable no-unused-vars */
  interface Window {
    openModal: (type: string, data?: unknown) => void
    closeModal: () => void
    verifyWebhook: (webhookId: string) => void
    verifyDomain: (domainId: string) => void
    vueModal: {
      open: (type: string, data?: unknown) => void
      close: () => void
    }
    toast: {
      success: (message: string, duration?: number) => string
      error: (message: string, duration?: number) => string
      warning: (message: string, duration?: number) => string
      info: (message: string, duration?: number) => string
      show: (message: string, type: 'success' | 'error' | 'warning' | 'info', duration?: number) => string
      clearAll: () => void
    }
  }
  /* eslint-enable no-unused-vars */
}

export {}
