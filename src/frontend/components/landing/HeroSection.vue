<template>
  <div class="bg-base-100">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20 lg:py-32">
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
        <!-- Hero Content -->
        <div class="space-y-8">
          <div class="space-y-4">
            <div class="flex items-center space-x-2">
              <div class="badge badge-secondary font-medium">GDPR compliant</div>
              <div class="badge badge-outline">🇪🇺 EU hosted & operated</div>
            </div>
            <h1 class="text-4xl lg:text-6xl font-bold text-base-content leading-tight">
              Turn <span class="text-primary">emails</span> into 
              <span class="text-secondary">webhooks</span>
            </h1>
            <p class="text-xl text-base-content/70 leading-relaxed">
              Deliver emails as structured webhooks. 
              <br/>100% hosted and operated in the EU.
            </p>
          </div>
          
          <div class="space-y-6">
            <!-- Benefits List -->
            <div class="space-y-3">
              <div class="flex items-center space-x-3">
                <div class="w-2 h-2 bg-success rounded-full"></div>
                <span class="text-base-content/80">Advanced email parsing for structured, programmatic handling</span>
              </div>
              <div class="flex items-center space-x-3">
                <div class="w-2 h-2 bg-success rounded-full"></div>
                <span class="text-base-content/80">Works seamlessly with n8n, Zapier, Make, or any webhook endpoint</span>
              </div>
              <div class="flex items-center space-x-3">
                <div class="w-2 h-2 bg-success rounded-full"></div>
                <span class="text-base-content/80">Multi-alias support to route different emails to different webhooks</span>
              </div>
              <div class="flex items-center space-x-3">
                <div class="w-2 h-2 bg-success rounded-full"></div>
                <span class="text-base-content/80">Fully EU-sovereign: made, hosted, and operated within Europe</span>
              </div>
            </div>
            
            <!-- CTA Buttons -->
            <div class="flex flex-col sm:flex-row gap-4">
              <router-link to="/register" class="btn btn-primary btn-lg">
                Get started free
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"/>
                </svg>
              </router-link>
              <a href="/docs" class="btn btn-outline btn-lg">
                View documentation
              </a>
            </div>
            
            <!-- Stats -->
            <div class="flex items-center space-x-8 pt-8 border-t border-base-300">
              <div class="text-center">
                <div class="text-2xl font-bold text-primary">99.9%</div>
                <div class="text-sm text-base-content/60">Delivery rate</div>
              </div>
              <div class="text-center">
                <div class="text-2xl font-bold text-primary">&lt;20s</div>
                <div class="text-sm text-base-content/60">Processing time</div>
              </div>
              <div class="text-center">
                <div class="text-2xl font-bold text-primary">100%</div>
                <div class="text-sm text-base-content/60">EU Sovereign — immune to US data laws</div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Hero Visual -->
        <div class="relative">
          <div class="bg-base-200 rounded-2xl p-8 shadow-lg">
            <!-- Email Flow Diagram -->
            <div class="space-y-4">
              <!-- Header -->
              <div class="text-center">
                <div class="text-xs text-base-content/60 font-medium uppercase tracking-wider">Multiple Email Aliases</div>
              </div>

              <!-- Multiple Email Inputs -->
              <div class="grid grid-cols-1 sm:grid-cols-3 gap-3">
                <!-- Sales Email -->
                <div class="flex flex-col items-center space-y-2 p-3 bg-base-100 rounded-lg border-2 border-dashed border-primary/30">
                  <div class="w-6 h-6 bg-primary/10 rounded-full flex items-center justify-center">
                    <svg class="w-3 h-3 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                    </svg>
                  </div>
                  <div class="text-center">
                    <div class="font-medium text-xs">sales@yourdomain</div>
                  </div>
                </div>

                <!-- Support Email -->
                <div class="flex flex-col items-center space-y-2 p-3 bg-base-100 rounded-lg border-2 border-dashed border-secondary/30">
                  <div class="w-6 h-6 bg-secondary/10 rounded-full flex items-center justify-center">
                    <svg class="w-3 h-3 text-secondary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                    </svg>
                  </div>
                  <div class="text-center">
                    <div class="font-medium text-xs">support@yourdomain</div>
                  </div>
                </div>

                <!-- Billing Email -->
                <div class="flex flex-col items-center space-y-2 p-3 bg-base-100 rounded-lg border-2 border-dashed border-accent/30">
                  <div class="w-6 h-6 bg-accent/10 rounded-full flex items-center justify-center">
                    <svg class="w-3 h-3 text-accent" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                    </svg>
                  </div>
                  <div class="text-center">
                    <div class="font-medium text-xs">billing@yourdomain</div>
                  </div>
                </div>
              </div>

              <!-- Converging Arrows -->
              <div class="flex justify-center relative">
                <div class="flex space-x-2">
                  <svg class="w-4 h-4 text-primary animate-bounce" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"/>
                  </svg>
                  <svg class="w-4 h-4 text-secondary animate-bounce" fill="none" stroke="currentColor" viewBox="0 0 24 24" style="animation-delay: 0.2s">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"/>
                  </svg>
                  <svg class="w-4 h-4 text-accent animate-bounce" fill="none" stroke="currentColor" viewBox="0 0 24 24" style="animation-delay: 0.4s">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"/>
                  </svg>
                </div>
              </div>
              
              <!-- Processing Hub -->
              <div class="text-center p-4 bg-gradient-to-r from-primary/10 via-secondary/10 to-accent/10 rounded-lg border border-base-300">
                <div class="font-medium text-base-content inline-flex items-center">
                  <Logo size="sm" text-class="text-lg" />
                </div>
                <div class="text-sm text-base-content/60">Parse, route & deliver by alias</div>
              </div>

              <!-- Diverging Arrows -->
              <div class="flex justify-center relative">
                <div class="flex space-x-2">
                  <svg class="w-4 h-4 text-primary animate-bounce" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"/>
                  </svg>
                  <svg class="w-4 h-4 text-secondary animate-bounce" fill="none" stroke="currentColor" viewBox="0 0 24 24" style="animation-delay: 0.2s">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"/>
                  </svg>
                  <svg class="w-4 h-4 text-accent animate-bounce" fill="none" stroke="currentColor" viewBox="0 0 24 24" style="animation-delay: 0.4s">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"/>
                  </svg>
                </div>
              </div>

              <!-- Header -->
              <div class="text-center">
                <div class="text-xs text-base-content/60 font-medium uppercase tracking-wider">Targeted Webhook Delivery</div>
              </div>

              <!-- Multiple Webhook Outputs -->
              <div class="grid grid-cols-1 sm:grid-cols-3 gap-3">
                <!-- CRM Webhook -->
                <div class="flex flex-col items-center space-y-2 p-3 bg-primary/10 rounded-lg border-2 border-primary/30">
                  <div class="w-6 h-6 bg-primary/20 rounded-full flex items-center justify-center">
                    <svg class="w-3 h-3 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                    </svg>
                  </div>
                  <div class="text-center">
                    <div class="font-medium text-xs">CRM system</div>
                    <div class="text-xs text-base-content/60">Sales leads & contacts</div>
                  </div>
                </div>

                <!-- Support Webhook -->
                <div class="flex flex-col items-center space-y-2 p-3 bg-secondary/10 rounded-lg border-2 border-secondary/30">
                  <div class="w-6 h-6 bg-secondary/20 rounded-full flex items-center justify-center">
                    <svg class="w-3 h-3 text-secondary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                    </svg>
                  </div>
                  <div class="text-center">
                    <div class="font-medium text-xs">Support tickets</div>
                    <div class="text-xs text-base-content/60">Zendesk, Freshdesk, etc.</div>
                  </div>
                </div>

                <!-- Billing Webhook -->
                <div class="flex flex-col items-center space-y-2 p-3 bg-accent/10 rounded-lg border-2 border-accent/30">
                  <div class="w-6 h-6 bg-accent/20 rounded-full flex items-center justify-center">
                    <svg class="w-3 h-3 text-accent" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                    </svg>
                  </div>
                  <div class="text-center">
                    <div class="font-medium text-xs">Billing system</div>
                    <div class="text-xs text-base-content/60">Invoices & payments</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- Floating elements -->
          <div class="absolute -top-4 -right-4 w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center">
            <svg class="w-8 h-8 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
          </div>
          
          <div class="absolute -bottom-4 -left-4 w-12 h-12 bg-secondary/20 rounded-full flex items-center justify-center">
            <svg class="w-6 h-6 text-secondary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
            </svg>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import Logo from '@/components/ui/Logo.vue'
</script>
